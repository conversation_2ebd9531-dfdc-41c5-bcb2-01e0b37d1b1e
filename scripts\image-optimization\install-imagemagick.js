#!/usr/bin/env node

/**
 * ImageMagick Installation Script (Node.js version)
 * A more reliable alternative to the PowerShell script
 */

import { spawn } from "child_process"
import { promisify } from "util"

const execAsync = promisify(spawn)

class ImageMagickInstaller {
  constructor() {
    this.isWindows = process.platform === "win32"
  }

  /**
   * Check if ImageMagick is installed
   */
  async checkImageMagick() {
    return new Promise((resolve) => {
      const process = spawn("magick", ["-version"], { stdio: "pipe" })

      let output = ""
      process.stdout.on("data", (data) => {
        output += data.toString()
      })

      process.on("close", (code) => {
        const isInstalled = code === 0 && output.includes("ImageMagick")
        resolve(isInstalled)
      })

      process.on("error", () => resolve(false))
    })
  }

  /**
   * Check if Chocolatey is installed
   */
  async checkChocolatey() {
    return new Promise((resolve) => {
      const process = spawn("choco", ["--version"], { stdio: "pipe" })
      process.on("close", (code) => resolve(code === 0))
      process.on("error", () => resolve(false))
    })
  }

  /**
   * Install Chocolatey
   */
  async installChocolatey() {
    console.log("🔧 Installing Chocolatey package manager...")

    if (!this.isWindows) {
      throw new Error("Chocolatey installation is only supported on Windows")
    }

    return new Promise((resolve, reject) => {
      const installScript = `
        Set-ExecutionPolicy Bypass -Scope Process -Force;
        [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072;
        iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))
      `

      const process = spawn(
        "powershell",
        ["-ExecutionPolicy", "Bypass", "-Command", installScript],
        { stdio: "inherit" }
      )

      process.on("close", (code) => {
        if (code === 0) {
          console.log("✅ Chocolatey installed successfully!")
          resolve(true)
        } else {
          reject(
            new Error(`Chocolatey installation failed with exit code ${code}`)
          )
        }
      })

      process.on("error", (error) => {
        reject(
          new Error(
            `Failed to execute Chocolatey installation: ${error.message}`
          )
        )
      })
    })
  }

  /**
   * Install ImageMagick via Chocolatey
   */
  async installImageMagick() {
    console.log("🖼️  Installing ImageMagick via Chocolatey...")

    return new Promise((resolve, reject) => {
      const process = spawn("choco", ["install", "imagemagick", "-y"], {
        stdio: "inherit",
      })

      process.on("close", (code) => {
        if (code === 0) {
          console.log("✅ ImageMagick installed successfully!")
          resolve(true)
        } else {
          reject(
            new Error(`ImageMagick installation failed with exit code ${code}`)
          )
        }
      })

      process.on("error", (error) => {
        reject(
          new Error(
            `Failed to execute ImageMagick installation: ${error.message}`
          )
        )
      })
    })
  }

  /**
   * Show ImageMagick version information
   */
  async showImageMagickInfo() {
    return new Promise((resolve) => {
      const process = spawn("magick", ["-version"], { stdio: "inherit" })

      process.on("close", () => {
        console.log("\n🎉 ImageMagick is ready for image optimization!")
        resolve()
      })

      process.on("error", () => {
        console.log("⚠️  Could not retrieve ImageMagick version information.")
        resolve()
      })
    })
  }

  /**
   * Main installation workflow
   */
  async install(options = {}) {
    const { force = false, checkOnly = false } = options

    console.log("\n🖼️  ImageMagick Installation")
    console.log("============================\n")

    try {
      // Check current installation status
      const isInstalled = await this.checkImageMagick()

      if (checkOnly) {
        if (isInstalled) {
          console.log("✅ ImageMagick is already installed and available.")
          await this.showImageMagickInfo()
          return true
        } else {
          console.log(
            "❌ ImageMagick is not installed or not available in PATH."
          )
          return false
        }
      }

      if (isInstalled && !force) {
        console.log("✅ ImageMagick is already installed!")
        await this.showImageMagickInfo()
        return true
      }

      if (force && isInstalled) {
        console.log("🔄 Force flag specified. Reinstalling ImageMagick...")
      }

      // Check if we're on Windows
      if (!this.isWindows) {
        console.log("❌ This installer currently only supports Windows.")
        console.log(
          "ℹ️  Please install ImageMagick manually for your operating system:"
        )
        console.log("   • macOS: brew install imagemagick")
        console.log("   • Ubuntu/Debian: sudo apt-get install imagemagick")
        console.log("   • Other: https://imagemagick.org/script/download.php")
        return false
      }

      // Check Chocolatey installation
      const hasChocolatey = await this.checkChocolatey()

      if (!hasChocolatey) {
        console.log("⚠️  Chocolatey package manager not found.")
        console.log("ℹ️  Installing Chocolatey first...")

        try {
          await this.installChocolatey()
        } catch (error) {
          console.log("❌ Failed to install Chocolatey:", error.message)
          console.log(
            "ℹ️  Please install Chocolatey manually: https://chocolatey.org/install"
          )
          return false
        }
      }

      // Install ImageMagick
      try {
        await this.installImageMagick()

        // Verify installation
        const isNowInstalled = await this.checkImageMagick()
        if (isNowInstalled) {
          console.log("\n🎉 Installation completed successfully!")
          await this.showImageMagickInfo()

          console.log("\n📋 Next Steps:")
          console.log(
            '• Run "pnpm images:analyze" to analyze your current images'
          )
          console.log(
            '• Run "pnpm images:optimize" to start the optimization process'
          )
          console.log('• Run "pnpm images:help" for more options')

          return true
        } else {
          console.log(
            "❌ Installation completed but ImageMagick is not available in PATH."
          )
          console.log(
            "ℹ️  You may need to restart your terminal or add ImageMagick to your PATH manually."
          )
          return false
        }
      } catch (error) {
        console.log("❌ Failed to install ImageMagick:", error.message)
        return false
      }
    } catch (error) {
      console.log("❌ Installation failed:", error.message)
      return false
    }
  }
}

// CLI usage - check if this file is being run directly
const isMainModule =
  process.argv[1] && process.argv[1].endsWith("install-imagemagick.js")

if (isMainModule) {
  const args = process.argv.slice(2)
  const force = args.includes("--force")
  const checkOnly = args.includes("--check-only")

  const installer = new ImageMagickInstaller()

  installer
    .install({ force, checkOnly })
    .then((success) => {
      process.exit(success ? 0 : 1)
    })
    .catch((error) => {
      console.error("❌ Installation error:", error.message)
      process.exit(1)
    })
}

export default ImageMagickInstaller
