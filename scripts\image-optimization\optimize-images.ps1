# Image Optimization PowerShell Script
# Handles the core ImageMagick operations for image optimization

param(
    [string]$InputPath,
    [string]$OutputPath,
    [string]$Format = "webp",
    [int]$Quality = 85,
    [int]$MaxSize = 150000,
    [switch]$Backup,
    [string]$BackupDir = "image-optimization-backups",
    [switch]$Verbose
)

# Set error action preference
$ErrorActionPreference = "Stop"

# Color output functions
function Write-Success { param($Message) Write-Host "✅ $Message" -ForegroundColor Green }
function Write-Info { param($Message) Write-Host "ℹ️  $Message" -ForegroundColor Cyan }
function Write-Warning { param($Message) Write-Host "⚠️  $Message" -ForegroundColor Yellow }
function Write-Error { param($Message) Write-Host "❌ $Message" -ForegroundColor Red }
function Write-Verbose { param($Message) if ($Verbose) { Write-Host "🔍 $Message" -ForegroundColor Gray } }

function Test-ImageMagick {
    try {
        $null = magick -version 2>$null
        return $true
    }
    catch {
        return $false
    }
}

function Get-FileSize {
    param([string]$FilePath)
    if (Test-Path $FilePath) {
        return (Get-Item $FilePath).Length
    }
    return 0
}

function Format-FileSize {
    param([long]$Bytes)
    if ($Bytes -ge 1MB) {
        return "{0:N2} MB" -f ($Bytes / 1MB)
    }
    elseif ($Bytes -ge 1KB) {
        return "{0:N2} KB" -f ($Bytes / 1KB)
    }
    else {
        return "$Bytes bytes"
    }
}

function Create-BackupCopy {
    param(
        [string]$SourcePath,
        [string]$BackupDirectory
    )
    
    try {
        if (-not (Test-Path $BackupDirectory)) {
            New-Item -ItemType Directory -Path $BackupDirectory -Force | Out-Null
            Write-Verbose "Created backup directory: $BackupDirectory"
        }
        
        $fileName = [System.IO.Path]::GetFileName($SourcePath)
        $timestamp = Get-Date -Format "yyyyMMdd-HHmmss"
        $backupFileName = "$timestamp-$fileName"
        $backupPath = Join-Path $BackupDirectory $backupFileName
        
        Copy-Item $SourcePath $backupPath -Force
        Write-Verbose "Created backup: $backupPath"
        return $backupPath
    }
    catch {
        Write-Warning "Failed to create backup for $SourcePath : $($_.Exception.Message)"
        return $null
    }
}

function Optimize-Image {
    param(
        [string]$InputFile,
        [string]$OutputFile,
        [string]$TargetFormat,
        [int]$TargetQuality,
        [int]$MaxFileSize
    )
    
    try {
        $originalSize = Get-FileSize $InputFile
        Write-Verbose "Processing: $InputFile ($(Format-FileSize $originalSize))"
        
        # Ensure output directory exists
        $outputDir = [System.IO.Path]::GetDirectoryName($OutputFile)
        if (-not (Test-Path $outputDir)) {
            New-Item -ItemType Directory -Path $outputDir -Force | Out-Null
        }
        
        # Build ImageMagick command
        $magickArgs = @()
        $magickArgs += $InputFile
        
        # Add optimization flags
        $magickArgs += "-strip"                    # Remove metadata
        $magickArgs += "-interlace", "Plane"       # Progressive loading
        $magickArgs += "-colorspace", "sRGB"       # Consistent color space
        
        # Format-specific optimizations
        switch ($TargetFormat.ToLower()) {
            "webp" {
                $magickArgs += "-define", "webp:lossless=false"
                $magickArgs += "-define", "webp:method=6"
                $magickArgs += "-define", "webp:alpha-quality=95"
                $magickArgs += "-quality", $TargetQuality
            }
            "jpeg" {
                $magickArgs += "-sampling-factor", "4:2:0"
                $magickArgs += "-define", "jpeg:dct-method=float"
                $magickArgs += "-quality", $TargetQuality
            }
            "png" {
                $magickArgs += "-define", "png:compression-filter=5"
                $magickArgs += "-define", "png:compression-level=9"
                $magickArgs += "-define", "png:compression-strategy=1"
                $magickArgs += "-quality", $TargetQuality
            }
            default {
                $magickArgs += "-quality", $TargetQuality
            }
        }
        
        $magickArgs += $OutputFile
        
        # Execute ImageMagick command
        Write-Verbose "Executing: magick $($magickArgs -join ' ')"
        & magick @magickArgs
        
        if ($LASTEXITCODE -ne 0) {
            throw "ImageMagick command failed with exit code $LASTEXITCODE"
        }
        
        # Check if output file was created and get its size
        if (Test-Path $OutputFile) {
            $optimizedSize = Get-FileSize $OutputFile
            $savings = $originalSize - $optimizedSize
            $savingsPercent = if ($originalSize -gt 0) { ($savings / $originalSize) * 100 } else { 0 }
            
            # Check if file size is within target
            if ($optimizedSize -le $MaxFileSize) {
                Write-Success "Optimized: $(Format-FileSize $originalSize) → $(Format-FileSize $optimizedSize) ($('{0:N1}' -f $savingsPercent)% reduction)"
                return @{
                    Success = $true
                    OriginalSize = $originalSize
                    OptimizedSize = $optimizedSize
                    Savings = $savings
                    SavingsPercent = $savingsPercent
                }
            }
            else {
                Write-Warning "File size $(Format-FileSize $optimizedSize) exceeds target $(Format-FileSize $MaxFileSize)"
                
                # Try with lower quality
                $lowerQuality = [math]::Max(50, $TargetQuality - 15)
                Write-Verbose "Retrying with lower quality: $lowerQuality"
                
                $retryArgs = $magickArgs.Clone()
                $qualityIndex = $retryArgs.IndexOf("-quality")
                if ($qualityIndex -ge 0) {
                    $retryArgs[$qualityIndex + 1] = $lowerQuality
                }
                
                & magick @retryArgs
                
                if ($LASTEXITCODE -eq 0 -and (Test-Path $OutputFile)) {
                    $retrySize = Get-FileSize $OutputFile
                    $retrySavings = $originalSize - $retrySize
                    $retrySavingsPercent = if ($originalSize -gt 0) { ($retrySavings / $originalSize) * 100 } else { 0 }
                    
                    Write-Info "Retry successful: $(Format-FileSize $originalSize) → $(Format-FileSize $retrySize) ($('{0:N1}' -f $retrySavingsPercent)% reduction)"
                    return @{
                        Success = $true
                        OriginalSize = $originalSize
                        OptimizedSize = $retrySize
                        Savings = $retrySavings
                        SavingsPercent = $retrySavingsPercent
                        QualityAdjusted = $true
                        FinalQuality = $lowerQuality
                    }
                }
            }
        }
        
        throw "Output file was not created or is invalid"
    }
    catch {
        Write-Error "Failed to optimize $InputFile : $($_.Exception.Message)"
        return @{
            Success = $false
            Error = $_.Exception.Message
        }
    }
}

# Main execution
if (-not $InputPath -or -not $OutputPath) {
    Write-Error "InputPath and OutputPath parameters are required"
    exit 1
}

if (-not (Test-ImageMagick)) {
    Write-Error "ImageMagick is not installed or not available in PATH"
    Write-Info "Run 'pnpm images:install' to install ImageMagick"
    exit 1
}

if (-not (Test-Path $InputPath)) {
    Write-Error "Input file not found: $InputPath"
    exit 1
}

# Create backup if requested
if ($Backup) {
    $backupPath = Create-BackupCopy $InputPath $BackupDir
    if ($backupPath) {
        Write-Info "Backup created: $backupPath"
    }
}

# Optimize the image
$result = Optimize-Image -InputFile $InputPath -OutputFile $OutputPath -TargetFormat $Format -TargetQuality $Quality -MaxFileSize $MaxSize

if ($result.Success) {
    Write-Success "Image optimization completed successfully!"
    
    # Output result as JSON for consumption by Node.js script
    $jsonResult = $result | ConvertTo-Json -Compress
    Write-Output "OPTIMIZATION_RESULT:$jsonResult"
} else {
    Write-Error "Image optimization failed: $($result.Error)"
    exit 1
}
