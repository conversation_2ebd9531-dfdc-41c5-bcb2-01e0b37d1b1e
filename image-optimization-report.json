{"summary": {"startTime": "2025-05-29T14:57:17.072Z", "endTime": "2025-05-29T14:57:17.359Z", "totalImages": 30, "analyzedImages": 30, "optimizedImages": 0, "totalSavings": 0, "errors": 10}, "optimized": [], "errors": ["Error processing public\\assets\\images\\misc\\red-flame.png: Cannot access 'process' before initialization", "Error processing public\\assets\\images\\misc\\blue-flame.png: Cannot access 'process' before initialization", "Error processing public\\assets\\images\\dragon\\neck.png: Cannot access 'process' before initialization", "Error processing public\\assets\\images\\dragon\\head.png: Cannot access 'process' before initialization", "Error processing public\\assets\\images\\dragon\\hand-right.png: Cannot access 'process' before initialization", "Error processing public\\assets\\images\\dragon\\hand-left.png: Cannot access 'process' before initialization", "Error processing public\\assets\\images\\dragon\\frame-top.png: Cannot access 'process' before initialization", "Error processing public\\assets\\images\\dragon\\frame-bottom.png: Cannot access 'process' before initialization", "Error processing public\\assets\\images\\dragon\\body-front.png: Cannot access 'process' before initialization", "Error processing public\\assets\\images\\dragon\\body-back.png: Cannot access 'process' before initialization"], "config": {"maxFileSizes": {"webp": 153600, "jpeg": 204800, "png": 307200, "gif": 512000, "svg": 51200, "default": 256000}, "quality": {"webp": 85, "jpeg": 85, "png": 95, "default": 85}, "supportedExtensions": [".jpg", ".jpeg", ".png", ".gif", ".bmp", ".tiff", ".tif", ".webp"], "excludedExtensions": [".svg"], "scanDirectories": ["public/assets/images", "src/assets", "assets"], "excludeDirectories": ["node_modules", "dist", "build", ".git", ".vscode", "coverage"], "excludePatterns": ["*.min.*", "*.optimized.*", "*-backup.*"], "backup": {"enabled": true, "directory": "image-optimization-backups", "timestamp": true, "keepOriginals": true}, "webpConversion": {"enabled": true, "replaceOriginals": false, "updateReferences": true, "fallbackSupport": true}, "codeUpdate": {"enabled": true, "fileExtensions": [".ts", ".tsx", ".js", ".jsx", ".css", ".scss", ".sass"], "excludeFiles": ["node_modules/**", "dist/**", "build/**", "*.min.js", "*.min.css"]}, "reporting": {"enabled": true, "outputFile": "image-optimization-report.json", "includeDetails": true, "showSavings": true}, "imagemagick": {"commonFlags": ["-strip", "-interlace", "Plane", "-gaussian-blur", "0.05", "-colorspace", "sRGB"], "formatFlags": {"jpeg": ["-sampling-factor", "4:2:0", "-define", "jpeg:dct-method=float"], "png": ["-define", "png:compression-filter=5", "-define", "png:compression-level=9", "-define", "png:compression-strategy=1"], "webp": ["-define", "webp:lossless=false", "-define", "webp:method=6", "-define", "webp:alpha-quality=95"]}}, "performance": {"maxConcurrentProcesses": 4, "chunkSize": 10, "timeoutMs": 30000}}}