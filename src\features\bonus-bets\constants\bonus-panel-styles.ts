/**
 * Style constants for the Bonus Bets Panel components
 */
export const BONUS_PANEL_STYLES = {
  mainContainer:
    "flex flex-col justify-center items-center h-full w-full p-2 text-white gap-1 overflow-hidden relative",
  contentContainer: "flex flex-col w-full justify-end",
  symbolsContainer: "grid auto-rows-min",

  topRow: "grid grid-cols-4 gap-1",
  bottomRow: "grid grid-cols-3 gap-1",

  symbolSize: "bonus-symbol h-auto",

  verticalText:
    "flex flex-col justify-center items-center text-center font-bold tracking-widest leading-tight gap-1",
} as const

/**
 * Dragon image assets for preloading
 */
export const DRAGON_ASSETS = [
  "/assets/images/dragon/head.webp",
  "/assets/images/dragon/neck.webp",
  "/assets/images/dragon/hand-right.webp",
  "/assets/images/dragon/hand-left.webp",
  "/assets/images/dragon/body-front.webp",
  "/assets/images/dragon/body-back.webp",
] as const

/**
 * Bet buttons configuration from bonus selectors
 */
export const getBetButtons = (bonusSelectors: any[]) => [
  bonusSelectors[6], //gold-bag
  bonusSelectors[1], //ying-yang
  bonusSelectors[2], //treasure-chest
  bonusSelectors[3], //chinese-fan
  bonusSelectors[4], //lantern
  bonusSelectors[5], //chinese-coin
  bonusSelectors[0], //lucky-pot
]
