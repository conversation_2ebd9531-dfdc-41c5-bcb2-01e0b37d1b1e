{"name": "roulette", "private": true, "version": "2.0.0", "type": "module", "scripts": {"dev": "node scripts/dev-server.js chrome dev", "dev:stg": "node scripts/dev-server.js chrome stg", "dev:prod": "node scripts/dev-server.js chrome prod", "dev:chrome": "node scripts/dev-server.js chrome dev", "dev:firefox": "node scripts/dev-server.js firefox dev", "dev:edge": "node scripts/dev-server.js edge dev", "dev:zen": "node scripts/dev-server.js zen dev", "dev:chrome:stg": "node scripts/dev-server.js chrome stg", "dev:firefox:stg": "node scripts/dev-server.js firefox stg", "dev:edge:stg": "node scripts/dev-server.js edge stg", "dev:zen:stg": "node scripts/dev-server.js zen stg", "build": "node scripts/build-help.js", "build:dev": "vite build --mode dev", "build:stg": "tspc -b && vite build --mode stg", "build:prod": "tspc -b && vite build --mode prod", "preview": "vite preview", "preview:dev": "vite preview --mode dev", "preview:stg": "vite preview --mode stg", "preview:prod": "vite preview --mode prod", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0 --fix ", "type-check": "tspc --noEmit", "clean": "<PERSON><PERSON><PERSON> dist", "clean:deps": "rimraf node_modules pnpm-lock.yaml && pnpm install", "env:list": "node scripts/env-setup.js list", "env:copy:stg": "node scripts/env-setup.js copy stg", "env:copy:prod": "node scripts/env-setup.js copy prod", "env:validate": "node scripts/env-setup.js validate dev", "env:info": "node scripts/env-setup.js info dev", "env:help": "node scripts/env-setup.js help", "images:optimize": "node scripts/image-optimizer.js", "images:install": "node scripts/image-optimization/install-imagemagick.js", "images:analyze": "node scripts/image-optimizer.js analyze", "images:convert": "node scripts/image-optimizer.js convert", "images:help": "node scripts/image-optimizer.js help"}, "dependencies": {"@maxmorozoff/try-catch-tuple": "^0.1.2", "@microsoft/signalr": "^8.0.7", "@million/lint": "^1.0.14", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-tooltip": "^1.1.8", "@tailwindcss/postcss": "^4.1.1", "@tailwindcss/vite": "^4.1.1", "@tanstack/react-query": "^5.71.5", "@types/crypto-js": "^4.2.2", "axios": "^1.8.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "crypto-js": "^4.2.0", "date-fns": "^4.1.0", "dompurify": "^3.2.4", "dotenv": "^16.5.0", "embla-carousel-react": "^8.5.2", "hls.js": "^1.6.0", "immer": "^10.1.1", "jwt-decode": "^4.0.0", "localforage": "^1.10.0", "lucide-react": "^0.487.0", "motion": "^12.6.3", "react-dom": "^19.1.0", "react-error-boundary": "^5.0.0", "react-hook-form": "^7.55.0", "react-router-dom": "^7.4.1", "react-window": "^1.8.11", "tailwind-merge": "^3.1.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.24.2", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/js": "^9.23.0", "@maxmorozoff/try-catch-tuple-ts-plugin": "^0.0.1", "@types/node": "^22.14.0", "@types/react": "^19.1.0", "@types/react-dom": "^19.1.1", "@types/react-window": "^1.8.8", "@vitejs/plugin-react-swc": "^3.8.1", "autoprefixer": "^10.4.21", "cross-env": "^7.0.3", "eslint": "^9.23.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "glob": "^11.0.2", "globals": "^16.0.0", "network-information-types": "^0.1.1", "postcss": "^8.5.3", "prettier-plugin-organize-imports": "^4.1.0", "prettier-plugin-tailwindcss": "^0.6.11", "react": "^19.1.0", "rimraf": "^6.0.1", "tailwindcss": "^4.1.1", "ts-patch": "^3.3.0", "typescript": "^5.8.2", "typescript-eslint": "^8.29.0", "vite": "^6.2.5"}, "pnpm": {"onlyBuiltDependencies": ["@swc/core", "esbuild"]}}