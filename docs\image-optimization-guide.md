# 🖼️ Image Optimization Workflow Guide

## Overview

This automated image optimization workflow provides a comprehensive solution for optimizing images in your TypeScript + React + Vite project. It integrates seamlessly with your existing build process and follows established patterns for build scripts.

## Features

- **Automated ImageMagick Installation**: PowerShell script to install ImageMagick CLI tools
- **Smart Image Discovery**: Recursively scans your codebase for image assets
- **Size-Aware Optimization**: Compresses images within configurable size thresholds
- **WebP Conversion**: Converts images to WebP format for better web performance
- **Code Reference Updates**: Automatically updates file references across your codebase
- **Backup & Recovery**: Creates backups before optimization
- **Detailed Reporting**: Generates comprehensive optimization reports

## Quick Start

### 1. Install ImageMagick

```bash
pnpm images:install
```

This will:
- Install Chocolatey package manager (if not present)
- Install ImageMagick CLI tools via Chocolatey
- Verify the installation

### 2. Analyze Your Images

```bash
pnpm images:analyze
```

This will scan your project and show:
- Total number of images found
- Current file sizes
- Which images need optimization
- Potential WebP conversion candidates

### 3. Run Full Optimization

```bash
pnpm images:optimize
```

This performs the complete workflow:
- Discovers all image assets
- Creates backups of original files
- Optimizes images within size thresholds
- Converts suitable images to WebP format
- Updates code references automatically
- Generates a detailed report

## Available Commands

| Command | Description |
|---------|-------------|
| `pnpm images:install` | Install ImageMagick CLI tools |
| `pnpm images:analyze` | Analyze images without optimization |
| `pnpm images:convert` | Convert images to WebP only |
| `pnpm images:optimize` | Full optimization workflow |
| `pnpm images:help` | Show help information |

## Configuration

The optimization behavior is controlled by `scripts/image-optimization/config.js`:

### Size Thresholds

```javascript
maxFileSizes: {
  webp: 150 * 1024,    // 150KB for WebP
  jpeg: 200 * 1024,    // 200KB for JPEG
  png: 300 * 1024,     // 300KB for PNG
  gif: 500 * 1024,     // 500KB for GIF
  default: 250 * 1024  // 250KB default
}
```

### Quality Settings

```javascript
quality: {
  webp: 85,      // High quality WebP
  jpeg: 85,      // High quality JPEG
  png: 95,       // PNG compression level
  default: 85    // Default quality
}
```

### Scan Directories

```javascript
scanDirectories: [
  'public/assets/images',
  'src/assets',
  'assets'
]
```

## How It Works

### 1. Image Discovery

The workflow scans configured directories for supported image formats:
- `.jpg`, `.jpeg`, `.png`, `.gif`, `.bmp`, `.tiff`, `.webp`
- Excludes `.svg` files (vector-based, don't need raster optimization)
- Skips already optimized files (patterns like `*.min.*`, `*.optimized.*`)

### 2. Analysis Phase

For each discovered image:
- Measures current file size
- Compares against target size thresholds
- Identifies WebP conversion candidates
- Calculates potential savings

### 3. Optimization Process

**Size Optimization:**
- Uses ImageMagick with optimized compression settings
- Strips metadata for smaller file sizes
- Applies progressive loading for better UX
- Retries with lower quality if size target isn't met

**WebP Conversion:**
- Converts JPEG, PNG, and other formats to WebP
- Maintains high quality while achieving better compression
- Preserves transparency for PNG images

### 4. Code Reference Updates

Automatically finds and updates image references in:
- TypeScript/JavaScript files (`.ts`, `.tsx`, `.js`, `.jsx`)
- CSS files (`.css`, `.scss`, `.sass`)
- Import statements
- JSX `src` attributes
- CSS `url()` references

### 5. Backup & Recovery

- Creates timestamped backups before optimization
- Stores backups in `image-optimization-backups/` directory
- Preserves original files for rollback if needed

## Integration with Existing Workflow

### Vite Configuration

The optimized images work seamlessly with your existing Vite setup:

```typescript
// vite.config.ts already includes asset handling
build: {
  rollupOptions: {
    output: {
      assetFileNames: "assets/[name]-[hash].[ext]"
    }
  }
}
```

### Image Loading Hooks

Your existing image loading infrastructure continues to work:

```typescript
// Existing hooks like useImageLoader will work with optimized images
const { loaded, error } = useImageLoader(src, id)
```

### Image Cache Integration

The optimization preserves compatibility with your image caching system:

```typescript
// Image cache continues to work with WebP images
await imageCache.preload(optimizedImagePath)
```

## Best Practices

### When to Run Optimization

- **Before major releases**: Ensure all images are optimized
- **After adding new images**: Run on new assets
- **Performance audits**: Regular optimization checks

### Recommended Workflow

1. **Development**: Add images normally during development
2. **Pre-commit**: Run `pnpm images:analyze` to check new images
3. **Pre-build**: Run `pnpm images:optimize` before production builds
4. **CI/CD Integration**: Include optimization in build pipeline

### Size Guidelines

The default thresholds are optimized for web performance:

- **WebP (150KB)**: Excellent compression, modern browser support
- **JPEG (200KB)**: Good for photos, wide compatibility
- **PNG (300KB)**: Necessary for transparency, larger due to lossless compression
- **GIF (500KB)**: Animations need more space

## Troubleshooting

### ImageMagick Installation Issues

```bash
# Check if ImageMagick is installed
magick -version

# Reinstall if needed
pnpm images:install
```

### Permission Errors

Run PowerShell as Administrator for initial installation:

```powershell
Set-ExecutionPolicy Bypass -Scope Process -Force
```

### Large File Processing

For very large images, increase timeout in config:

```javascript
performance: {
  timeoutMs: 60000  // 60 seconds
}
```

### Code Reference Issues

Run in dry-run mode to preview changes:

```bash
node scripts/image-optimization/update-references.js image.png image.webp --dry-run
```

## Reporting

After optimization, check the generated report:

```json
// image-optimization-report.json
{
  "summary": {
    "totalImages": 45,
    "optimizedImages": 23,
    "totalSavings": 2048576,
    "errors": 0
  },
  "optimized": [...],
  "errors": [...]
}
```

## Performance Impact

Expected improvements:
- **File size reduction**: 30-70% smaller images
- **Load time improvement**: Faster page loads
- **Bandwidth savings**: Reduced data usage
- **SEO benefits**: Better Core Web Vitals scores

## Support

For issues or questions:
1. Check the generated report for error details
2. Review the configuration settings
3. Verify ImageMagick installation
4. Check file permissions and paths
